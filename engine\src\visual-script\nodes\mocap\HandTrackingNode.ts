/**
 * 手部追踪节点
 * 使用MediaPipe进行手部关键点检测和手势识别
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3 } from 'three';

/**
 * 关键点数据接口
 */
export interface LandmarkData {
  x: number;
  y: number;
  z?: number;
  visibility?: number;
}

/**
 * 手势类型枚举
 */
export enum GestureType {
  GRAB = 'grab',
  OPEN_HAND = 'open_hand',
  POINTING = 'pointing',
  THUMBS_UP = 'thumbs_up',
  PEACE = 'peace',
  FIST = 'fist'
}

/**
 * 手势识别结果
 */
export interface GestureResult {
  type: GestureType;
  confidence: number;
  hand: 'left' | 'right';
  position: Vector3;
  timestamp: number;
}

/**
 * 手部检测结果
 */
export interface HandResults {
  leftHand?: LandmarkData[];
  rightHand?: LandmarkData[];
  handedness?: any[];
  confidence: number;
}

/**
 * 简化的MediaPipe检测器接口
 */
interface SimpleHandDetector {
  initialize(): Promise<void>;
  destroy(): void;
  detectHands(imageData: ImageData): Promise<HandResults>;
  on(event: string, callback: Function): void;
}

/**
 * 手部追踪节点配置
 */
export interface HandTrackingNodeConfig {
  /** 最大手部数量 */
  maxNumHands: number;
  /** 模型复杂度 (0-1) */
  modelComplexity: number;
  /** 最小检测置信度 */
  minDetectionConfidence: number;
  /** 最小跟踪置信度 */
  minTrackingConfidence: number;
  /** 是否启用手势识别 */
  enableGestureRecognition: boolean;
  /** 手势置信度阈值 */
  gestureConfidenceThreshold: number;
  /** 是否自动初始化 */
  autoInitialize: boolean;
}

/**
 * 手部追踪节点
 */
export class HandTrackingNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'HandTracking';

  /** 节点名称 */
  public static readonly NAME = '手部追踪';

  /** 节点描述 */
  public static readonly DESCRIPTION = '使用MediaPipe检测手部关键点和识别手势';

  private handDetector: SimpleHandDetector | null = null;
  private config: HandTrackingNodeConfig;
  private isInitialized = false;
  private lastResults: HandResults | null = null;
  private lastGestures: { left?: GestureResult; right?: GestureResult } = {};
  private processingCount = 0;
  private successCount = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: HandTrackingNodeConfig = {
    maxNumHands: 2,
    modelComplexity: 1,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5,
    enableGestureRecognition: true,
    gestureConfidenceThreshold: 0.6,
    autoInitialize: true
  };

  constructor(nodeType: string = HandTrackingNode.TYPE, name: string = HandTrackingNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...HandTrackingNode.DEFAULT_CONFIG };
    this.setupPorts();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('imageData', 'object', '图像数据');
    this.addInput('detect', 'trigger', '检测');
    this.addInput('initialize', 'trigger', '初始化');
    this.addInput('maxNumHands', 'number', '最大手数');
    this.addInput('minDetectionConfidence', 'number', '检测置信度');
    this.addInput('enableGestureRecognition', 'boolean', '启用手势识别');

    // 基本输出端口
    this.addOutput('leftHand', 'array', '左手关键点');
    this.addOutput('rightHand', 'array', '右手关键点');
    this.addOutput('handedness', 'array', '手部分类');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('handsDetected', 'boolean', '检测到手部');
    this.addOutput('handCount', 'number', '手部数量');
    this.addOutput('processingTime', 'number', '处理时间');
    this.addOutput('successRate', 'number', '成功率');

    // 事件输出端口
    this.addOutput('onDetected', 'trigger', '检测完成');
    this.addOutput('onInitialized', 'trigger', '初始化完成');
    this.addOutput('onError', 'trigger', '错误');

    // 手势识别输出端口
    this.addOutput('leftGesture', 'object', '左手手势');
    this.addOutput('rightGesture', 'object', '右手手势');
    this.addOutput('onGestureChanged', 'trigger', '手势变化');

    // 左手关键点输出
    this.addOutput('leftWrist', 'object', '左手腕');
    this.addOutput('leftThumb', 'array', '左拇指');
    this.addOutput('leftIndex', 'array', '左食指');
    this.addOutput('leftMiddle', 'array', '左中指');
    this.addOutput('leftRing', 'array', '左无名指');
    this.addOutput('leftPinky', 'array', '左小指');

    // 右手关键点输出
    this.addOutput('rightWrist', 'object', '右手腕');
    this.addOutput('rightThumb', 'array', '右拇指');
    this.addOutput('rightIndex', 'array', '右食指');
    this.addOutput('rightMiddle', 'array', '右中指');
    this.addOutput('rightRing', 'array', '右无名指');
    this.addOutput('rightPinky', 'array', '右小指');

    // 手部位置和方向输出
    this.addOutput('leftHandPosition', 'object', '左手位置');
    this.addOutput('rightHandPosition', 'object', '右手位置');
    this.addOutput('leftHandRotation', 'object', '左手旋转');
    this.addOutput('rightHandRotation', 'object', '右手旋转');
  }

  /**
   * 执行节点
   */
  public execute(inputs?: any): any {
    try {
      // 检查输入
      const imageData = inputs?.imageData as ImageData;
      const detectTrigger = inputs?.detect;
      const initializeTrigger = inputs?.initialize;
      const maxNumHands = inputs?.maxNumHands as number;
      const minDetectionConfidence = inputs?.minDetectionConfidence as number;
      const enableGestureRecognition = inputs?.enableGestureRecognition as boolean;

      // 更新配置
      if (maxNumHands !== undefined) {
        this.config.maxNumHands = Math.max(1, Math.min(4, Math.floor(maxNumHands)));
      }
      if (minDetectionConfidence !== undefined) {
        this.config.minDetectionConfidence = Math.max(0, Math.min(1, minDetectionConfidence));
      }
      if (enableGestureRecognition !== undefined) {
        this.config.enableGestureRecognition = enableGestureRecognition;
      }

      // 处理初始化触发
      if (initializeTrigger || (!this.isInitialized && this.config.autoInitialize)) {
        this.initializeDetector();
      }

      // 处理检测触发
      if (detectTrigger && imageData && this.isInitialized) {
        this.detectHands(imageData);
      }

      // 返回输出
      return this.getOutputs();

    } catch (error) {
      Debug.error('HandTrackingNode', '节点执行失败', String(error));
      return { onError: true };
    }
  }

  /**
   * 获取输出值
   */
  private getOutputs(): any {
    const results = this.lastResults;
    if (!results) {
      return {
        handsDetected: false,
        handCount: 0,
        leftHand: [],
        rightHand: [],
        confidence: 0,
        onError: false
      };
    }

    return {
      leftHand: results.leftHand || [],
      rightHand: results.rightHand || [],
      handedness: results.handedness || [],
      confidence: results.confidence,
      handsDetected: (results.leftHand || results.rightHand) ? true : false,
      handCount: (results.leftHand ? 1 : 0) + (results.rightHand ? 1 : 0),
      leftGesture: this.lastGestures.left || null,
      rightGesture: this.lastGestures.right || null,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,
      onDetected: false,
      onInitialized: false,
      onError: false,
      onGestureChanged: false
    };
  }

  /**
   * 初始化检测器
   */
  private initializeDetector(): void {
    try {
      // 模拟初始化
      this.isInitialized = true;
      this.processingCount = 0;
      this.successCount = 0;

      Debug.log('HandTrackingNode', '手部检测器初始化成功');

    } catch (error) {
      Debug.error('HandTrackingNode', '初始化检测器失败', String(error));
      this.isInitialized = false;
    }
  }

  /**
   * 检测手部
   */
  private detectHands(imageData: ImageData): void {
    if (!this.isInitialized) {
      Debug.error('HandTrackingNode', '检测器未初始化');
      return;
    }

    try {
      this.processingCount++;

      // 模拟检测结果
      const mockResults: HandResults = {
        leftHand: this.generateMockHandLandmarks(),
        rightHand: this.generateMockHandLandmarks(),
        handedness: ['Left', 'Right'],
        confidence: 0.8
      };

      this.lastResults = mockResults;
      this.successCount++;

      // 处理手势识别
      if (this.config.enableGestureRecognition) {
        this.processGestureRecognition(mockResults);
      }

      Debug.log('HandTrackingNode', '手部检测完成');

    } catch (error) {
      Debug.error('HandTrackingNode', '手部检测失败', String(error));
    }
  }

  /**
   * 生成模拟手部关键点
   */
  private generateMockHandLandmarks(): LandmarkData[] {
    const landmarks: LandmarkData[] = [];
    for (let i = 0; i < 21; i++) {
      landmarks.push({
        x: Math.random(),
        y: Math.random(),
        z: Math.random() * 0.1,
        visibility: 0.9
      });
    }
    return landmarks;
  }

  /**
   * 处理手势识别
   */
  private processGestureRecognition(results: HandResults): void {
    const newGestures: { left?: GestureResult; right?: GestureResult } = {};

    // 识别左手手势
    if (results.leftHand) {
      const leftGesture = this.recognizeGesture(results.leftHand, 'left');
      if (leftGesture && leftGesture.confidence > this.config.gestureConfidenceThreshold) {
        newGestures.left = leftGesture;
      }
    }

    // 识别右手手势
    if (results.rightHand) {
      const rightGesture = this.recognizeGesture(results.rightHand, 'right');
      if (rightGesture && rightGesture.confidence > this.config.gestureConfidenceThreshold) {
        newGestures.right = rightGesture;
      }
    }

    // 更新手势状态
    this.lastGestures = newGestures;
  }

  /**
   * 识别手势
   */
  private recognizeGesture(handLandmarks: LandmarkData[], handType: 'left' | 'right'): GestureResult | null {
    if (handLandmarks.length < 21) {
      return null;
    }

    try {
      // 计算手指弯曲度
      const fingerCurvatures = this.calculateFingerCurvatures(handLandmarks);

      // 计算手部位置
      const handPosition = this.calculateHandPosition(handLandmarks);

      // 抓取手势检测
      if (this.isGrabGesture(fingerCurvatures)) {
        return {
          type: GestureType.GRAB,
          confidence: this.calculateGrabConfidence(fingerCurvatures),
          hand: handType,
          position: handPosition,
          timestamp: Date.now()
        };
      }

      // 张开手势检测
      if (this.isOpenHandGesture(fingerCurvatures)) {
        return {
          type: GestureType.OPEN_HAND,
          confidence: this.calculateOpenHandConfidence(fingerCurvatures),
          hand: handType,
          position: handPosition,
          timestamp: Date.now()
        };
      }

      // 指向手势检测
      if (this.isPointingGesture(fingerCurvatures)) {
        return {
          type: GestureType.POINTING,
          confidence: this.calculatePointingConfidence(fingerCurvatures),
          hand: handType,
          position: handPosition,
          timestamp: Date.now()
        };
      }

      // 竖拇指手势检测
      if (this.isThumbsUpGesture(fingerCurvatures)) {
        return {
          type: GestureType.THUMBS_UP,
          confidence: this.calculateThumbsUpConfidence(fingerCurvatures),
          hand: handType,
          position: handPosition,
          timestamp: Date.now()
        };
      }

    } catch (error) {
      Debug.error('HandTrackingNode', '手势识别失败', error);
    }

    return null;
  }

  /**
   * 计算手指弯曲度
   */
  private calculateFingerCurvatures(handLandmarks: LandmarkData[]): any {
    // MediaPipe手部关键点索引
    const fingerTips = [4, 8, 12, 16, 20]; // 拇指、食指、中指、无名指、小指指尖
    const fingerPips = [3, 6, 10, 14, 18]; // 对应的PIP关节
    const fingerMcps = [2, 5, 9, 13, 17]; // 对应的MCP关节

    const curvatures = {
      thumb: 0,
      index: 0,
      middle: 0,
      ring: 0,
      pinky: 0
    };

    const fingerNames = ['thumb', 'index', 'middle', 'ring', 'pinky'] as const;

    for (let i = 0; i < 5; i++) {
      const tip = handLandmarks[fingerTips[i]];
      const pip = handLandmarks[fingerPips[i]];
      const mcp = handLandmarks[fingerMcps[i]];

      if (tip && pip && mcp) {
        // 计算弯曲度：指尖到MCP的距离 vs PIP到MCP的距离
        const tipToMcp = Math.sqrt(
          Math.pow(tip.x - mcp.x, 2) +
          Math.pow(tip.y - mcp.y, 2)
        );
        const pipToMcp = Math.sqrt(
          Math.pow(pip.x - mcp.x, 2) +
          Math.pow(pip.y - mcp.y, 2)
        );

        // 弯曲度 = 1 - (实际距离 / 最大可能距离)
        curvatures[fingerNames[i]] = Math.max(0, 1 - (tipToMcp / (pipToMcp * 2)));
      }
    }

    return curvatures;
  }

  /**
   * 计算手部位置
   */
  private calculateHandPosition(handLandmarks: LandmarkData[]): Vector3 {
    // 使用手腕位置作为手部中心
    const wrist = handLandmarks[0]; // MediaPipe手部关键点0是手腕

    if (wrist) {
      return new Vector3(
        (wrist.x - 0.5) * 2, // 转换到[-1, 1]范围
        -(wrist.y - 0.5) * 2, // Y轴翻转
        wrist.z || 0
      );
    }

    return new Vector3();
  }

  /**
   * 检测抓取手势
   */
  private isGrabGesture(curvatures: any): boolean {
    const threshold = 0.7;
    return curvatures.index > threshold &&
           curvatures.middle > threshold &&
           curvatures.ring > threshold &&
           curvatures.pinky > threshold;
  }

  /**
   * 检测张开手势
   */
  private isOpenHandGesture(curvatures: any): boolean {
    const threshold = 0.3;
    return curvatures.thumb < threshold &&
           curvatures.index < threshold &&
           curvatures.middle < threshold &&
           curvatures.ring < threshold &&
           curvatures.pinky < threshold;
  }

  /**
   * 检测指向手势
   */
  private isPointingGesture(curvatures: any): boolean {
    const straightThreshold = 0.3;
    const bentThreshold = 0.7;
    return curvatures.index < straightThreshold &&
           curvatures.middle > bentThreshold &&
           curvatures.ring > bentThreshold &&
           curvatures.pinky > bentThreshold;
  }

  /**
   * 检测竖拇指手势
   */
  private isThumbsUpGesture(curvatures: any): boolean {
    const straightThreshold = 0.3;
    const bentThreshold = 0.7;
    return curvatures.thumb < straightThreshold &&
           curvatures.index > bentThreshold &&
           curvatures.middle > bentThreshold &&
           curvatures.ring > bentThreshold &&
           curvatures.pinky > bentThreshold;
  }

  /**
   * 计算各种手势的置信度
   */
  private calculateGrabConfidence(curvatures: any): number {
    const avgCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return Math.min(1, avgCurvature);
  }

  private calculateOpenHandConfidence(curvatures: any): number {
    const avgStraightness = 1 - (curvatures.thumb + curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 5;
    return Math.max(0, avgStraightness);
  }

  private calculatePointingConfidence(curvatures: any): number {
    const indexStraightness = 1 - curvatures.index;
    const othersCurvature = (curvatures.middle + curvatures.ring + curvatures.pinky) / 3;
    return (indexStraightness + othersCurvature) / 2;
  }

  private calculateThumbsUpConfidence(curvatures: any): number {
    const thumbStraightness = 1 - curvatures.thumb;
    const othersCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return (thumbStraightness + othersCurvature) / 2;
  }



  /**
   * 计算手部旋转
   */
  private calculateHandRotation(handLandmarks: LandmarkData[]): Vector3 {
    if (handLandmarks.length < 21) {
      return new Vector3();
    }

    // 使用手腕、中指MCP和食指MCP计算手部方向
    const wrist = handLandmarks[0];
    const middleMcp = handLandmarks[9];
    const indexMcp = handLandmarks[5];

    if (wrist && middleMcp && indexMcp) {
      // 计算手部向前方向（手腕到中指MCP）
      const forward = new Vector3(
        middleMcp.x - wrist.x,
        middleMcp.y - wrist.y,
        (middleMcp.z || 0) - (wrist.z || 0)
      ).normalize();

      // 计算手部右方向（手腕到食指MCP）
      const right = new Vector3(
        indexMcp.x - wrist.x,
        indexMcp.y - wrist.y,
        (indexMcp.z || 0) - (wrist.z || 0)
      ).normalize();

      // 计算手部上方向
      const up = new Vector3().crossVectors(forward, right).normalize();

      // 转换为欧拉角
      const pitch = Math.asin(-forward.y);
      const yaw = Math.atan2(forward.x, forward.z);
      const roll = Math.atan2(right.y, up.y);

      return new Vector3(pitch, yaw, roll);
    }

    return new Vector3();
  }

  /**
   * 获取节点配置
   */
  public getConfig(): HandTrackingNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<HandTrackingNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取最后检测结果
   */
  public getLastResults(): HandResults | null {
    return this.lastResults;
  }

  /**
   * 获取最后手势结果
   */
  public getLastGestures(): { left?: GestureResult; right?: GestureResult } {
    return { ...this.lastGestures };
  }

  /**
   * 获取检测成功率
   */
  public getSuccessRate(): number {
    return this.processingCount > 0 ? this.successCount / this.processingCount : 0;
  }

  /**
   * 是否已初始化
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }
}