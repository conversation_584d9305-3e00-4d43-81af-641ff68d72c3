/**
 * 增强的水体渲染器
 * 提供高质量的水体渲染效果，包括反射、折射、波浪、泡沫等
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 增强水体渲染配置
 */
export interface EnhancedWaterRenderConfig {
  /** 水体质量等级 */
  quality: 'low' | 'medium' | 'high' | 'ultra';
  /** 是否启用反射 */
  enableReflection: boolean;
  /** 是否启用折射 */
  enableRefraction: boolean;
  /** 是否启用波浪 */
  enableWaves: boolean;
  /** 是否启用泡沫 */
  enableFoam: boolean;
  /** 是否启用焦散 */
  enableCaustics: boolean;
  /** 是否启用水下效果 */
  enableUnderwater: boolean;
  /** 反射分辨率 */
  reflectionResolution: number;
  /** 折射分辨率 */
  refractionResolution: number;
  /** 波浪细节等级 */
  waveDetail: number;
  /** 性能优化 */
  performance: {
    enableLOD: boolean;
    enableInstancing: boolean;
    enableFrustumCulling: boolean;
    maxDrawDistance: number;
  };
}

/**
 * 水体材质参数
 */
export interface WaterMaterialParams {
  /** 水体颜色 */
  color: THREE.Color;
  /** 透明度 */
  opacity: number;
  /** 折射率 */
  refractionRatio: number;
  /** 波浪强度 */
  waveStrength: number;
  /** 波浪速度 */
  waveSpeed: number;
  /** 波浪频率 */
  waveFrequency: number;
  /** 泡沫阈值 */
  foamThreshold: number;
  /** 泡沫强度 */
  foamStrength: number;
  /** 深度衰减 */
  depthFalloff: number;
  /** 散射系数 */
  scattering: number;
}

/**
 * 增强水体渲染器
 */
export class EnhancedWaterRenderer extends System {
  public static readonly TYPE = 'EnhancedWaterRenderer';

  private config: EnhancedWaterRenderConfig;
  private waterBodies: Map<string, WaterBodyComponent>;
  private reflectionCamera: THREE.Camera;
  private refractionCamera: THREE.Camera;
  private reflectionRenderTarget: THREE.WebGLRenderTarget;
  private refractionRenderTarget: THREE.WebGLRenderTarget;
  private waterMaterial: THREE.ShaderMaterial;
  private waveTexture: THREE.DataTexture;
  private foamTexture: THREE.Texture;
  private causticsTexture: THREE.Texture;
  private performanceMonitor: PerformanceMonitor;
  private time: number;

  constructor(config: EnhancedWaterRenderConfig) {
    super();
    this.config = config;
    this.waterBodies = new Map();
    this.performanceMonitor = new PerformanceMonitor();
    this.time = 0;

    this.initializeRenderTargets();
    this.initializeCameras();
    this.initializeTextures();
    this.initializeWaterMaterial();
  }

  /**
   * 初始化渲染目标
   */
  private initializeRenderTargets(): void {
    // 反射渲染目标
    this.reflectionRenderTarget = new THREE.WebGLRenderTarget(
      this.config.reflectionResolution,
      this.config.reflectionResolution,
      {
        format: THREE.RGBAFormat,
        type: THREE.FloatType,
        generateMipmaps: true,
        minFilter: THREE.LinearMipmapLinearFilter,
        magFilter: THREE.LinearFilter
      }
    );

    // 折射渲染目标
    this.refractionRenderTarget = new THREE.WebGLRenderTarget(
      this.config.refractionResolution,
      this.config.refractionResolution,
      {
        format: THREE.RGBAFormat,
        type: THREE.FloatType,
        generateMipmaps: true,
        minFilter: THREE.LinearMipmapLinearFilter,
        magFilter: THREE.LinearFilter
      }
    );

    Debug.log('水体渲染目标初始化完成', {
      reflectionResolution: this.config.reflectionResolution,
      refractionResolution: this.config.refractionResolution
    });
  }

  /**
   * 初始化相机
   */
  private initializeCameras(): void {
    this.reflectionCamera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    this.refractionCamera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
  }

  /**
   * 初始化纹理
   */
  private initializeTextures(): void {
    // 生成波浪纹理
    this.generateWaveTexture();
    
    // 加载泡沫纹理
    this.loadFoamTexture();
    
    // 生成焦散纹理
    this.generateCausticsTexture();
  }

  /**
   * 生成波浪纹理
   */
  private generateWaveTexture(): void {
    const size = 512;
    const data = new Float32Array(size * size * 4);

    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        const index = (i * size + j) * 4;
        const x = (i / size) * 2 - 1;
        const y = (j / size) * 2 - 1;

        // 生成法线贴图数据
        const wave1 = Math.sin(x * 10 + y * 5) * 0.5;
        const wave2 = Math.cos(x * 7 - y * 8) * 0.3;
        const wave3 = Math.sin(x * 15 + y * 12) * 0.2;

        const height = wave1 + wave2 + wave3;
        
        // 计算法线
        const dx = Math.cos(x * 10 + y * 5) * 5 + Math.sin(x * 7 - y * 8) * 7 + Math.cos(x * 15 + y * 12) * 15;
        const dy = Math.cos(x * 10 + y * 5) * 10 - Math.cos(x * 7 - y * 8) * 8 + Math.cos(x * 15 + y * 12) * 12;

        const normal = new THREE.Vector3(-dx, -dy, 1).normalize();

        data[index] = normal.x * 0.5 + 0.5;     // R: Normal X
        data[index + 1] = normal.y * 0.5 + 0.5; // G: Normal Y
        data[index + 2] = normal.z * 0.5 + 0.5; // B: Normal Z
        data[index + 3] = height * 0.5 + 0.5;   // A: Height
      }
    }

    this.waveTexture = new THREE.DataTexture(data, size, size, THREE.RGBAFormat, THREE.FloatType);
    this.waveTexture.wrapS = THREE.RepeatWrapping;
    this.waveTexture.wrapT = THREE.RepeatWrapping;
    this.waveTexture.needsUpdate = true;
  }

  /**
   * 加载泡沫纹理
   */
  private loadFoamTexture(): void {
    // 这里应该加载实际的泡沫纹理
    // 目前使用程序生成的纹理
    const size = 256;
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d')!;

    // 生成泡沫图案
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, size, size);
    
    ctx.fillStyle = '#ffffff';
    for (let i = 0; i < 100; i++) {
      const x = Math.random() * size;
      const y = Math.random() * size;
      const radius = Math.random() * 10 + 2;
      
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fill();
    }

    this.foamTexture = new THREE.CanvasTexture(canvas);
    this.foamTexture.wrapS = THREE.RepeatWrapping;
    this.foamTexture.wrapT = THREE.RepeatWrapping;
  }

  /**
   * 生成焦散纹理
   */
  private generateCausticsTexture(): void {
    const size = 512;
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d')!;

    // 生成焦散图案
    const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
    gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.5)');
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, size, size);

    this.causticsTexture = new THREE.CanvasTexture(canvas);
    this.causticsTexture.wrapS = THREE.RepeatWrapping;
    this.causticsTexture.wrapT = THREE.RepeatWrapping;
  }

  /**
   * 初始化水体材质
   */
  private initializeWaterMaterial(): void {
    const vertexShader = `
      varying vec2 vUv;
      varying vec3 vWorldPosition;
      varying vec3 vNormal;
      varying vec3 vViewPosition;
      
      void main() {
        vUv = uv;
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        vNormal = normalize(normalMatrix * normal);
        
        vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
        vViewPosition = -mvPosition.xyz;
        
        gl_Position = projectionMatrix * mvPosition;
      }
    `;

    const fragmentShader = `
      uniform float time;
      uniform vec3 waterColor;
      uniform float opacity;
      uniform float waveStrength;
      uniform float waveSpeed;
      uniform sampler2D waveTexture;
      uniform sampler2D reflectionTexture;
      uniform sampler2D refractionTexture;
      uniform sampler2D foamTexture;
      uniform sampler2D causticsTexture;
      uniform bool enableReflection;
      uniform bool enableRefraction;
      uniform bool enableFoam;
      uniform bool enableCaustics;
      
      varying vec2 vUv;
      varying vec3 vWorldPosition;
      varying vec3 vNormal;
      varying vec3 vViewPosition;
      
      void main() {
        vec2 waveUv = vUv * 4.0 + time * waveSpeed * 0.1;
        vec4 waveData = texture2D(waveTexture, waveUv);
        
        // 计算扰动的法线
        vec3 normal = normalize(vNormal);
        normal.xy += (waveData.xy - 0.5) * waveStrength;
        normal = normalize(normal);
        
        // 计算反射和折射坐标
        vec3 viewDirection = normalize(vViewPosition);
        vec2 reflectionUv = vUv + normal.xy * 0.1;
        vec2 refractionUv = vUv + normal.xy * 0.05;
        
        vec3 finalColor = waterColor;
        
        // 反射
        if (enableReflection) {
          vec3 reflection = texture2D(reflectionTexture, reflectionUv).rgb;
          float fresnel = pow(1.0 - dot(normal, viewDirection), 2.0);
          finalColor = mix(finalColor, reflection, fresnel * 0.8);
        }
        
        // 折射
        if (enableRefraction) {
          vec3 refraction = texture2D(refractionTexture, refractionUv).rgb;
          finalColor = mix(refraction, finalColor, 0.3);
        }
        
        // 泡沫
        if (enableFoam) {
          float foam = texture2D(foamTexture, vUv * 8.0 + time * 0.1).r;
          foam *= smoothstep(0.0, 0.1, waveData.a);
          finalColor = mix(finalColor, vec3(1.0), foam * 0.5);
        }
        
        // 焦散
        if (enableCaustics) {
          float caustics = texture2D(causticsTexture, vUv * 2.0 + time * 0.05).r;
          finalColor += caustics * 0.2;
        }
        
        gl_FragColor = vec4(finalColor, opacity);
      }
    `;

    this.waterMaterial = new THREE.ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        time: { value: 0 },
        waterColor: { value: new THREE.Color(0x006994) },
        opacity: { value: 0.8 },
        waveStrength: { value: 0.1 },
        waveSpeed: { value: 1.0 },
        waveTexture: { value: this.waveTexture },
        reflectionTexture: { value: this.reflectionRenderTarget.texture },
        refractionTexture: { value: this.refractionRenderTarget.texture },
        foamTexture: { value: this.foamTexture },
        causticsTexture: { value: this.causticsTexture },
        enableReflection: { value: this.config.enableReflection },
        enableRefraction: { value: this.config.enableRefraction },
        enableFoam: { value: this.config.enableFoam },
        enableCaustics: { value: this.config.enableCaustics }
      },
      transparent: true,
      side: THREE.DoubleSide
    });

    Debug.log('水体材质初始化完成');
  }

  /**
   * 添加水体
   */
  public addWaterBody(id: string, waterBody: WaterBodyComponent): void {
    this.waterBodies.set(id, waterBody);
    
    // 为水体设置增强材质
    if (waterBody.mesh) {
      waterBody.mesh.material = this.waterMaterial;
    }

    Debug.log(`添加水体: ${id}`);
  }

  /**
   * 移除水体
   */
  public removeWaterBody(id: string): void {
    this.waterBodies.delete(id);
    Debug.log(`移除水体: ${id}`);
  }

  /**
   * 重写系统更新方法
   */
  public update(deltaTime: number): void {
    this.time += deltaTime;

    // 更新材质时间
    this.waterMaterial.uniforms.time.value = this.time;

    // 更新波浪纹理
    if (this.config.enableWaves) {
      this.updateWaveTexture();
    }
  }

  /**
   * 渲染更新（需要外部调用）
   */
  public render(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera): void {
    // 渲染反射
    if (this.config.enableReflection) {
      this.renderReflection(renderer, scene, camera);
    }

    // 渲染折射
    if (this.config.enableRefraction) {
      this.renderRefraction(renderer, scene, camera);
    }
  }

  /**
   * 渲染反射
   */
  private renderReflection(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera): void {
    // 设置反射相机
    this.reflectionCamera.copy(camera);
    this.reflectionCamera.position.y = -camera.position.y;
    this.reflectionCamera.lookAt(0, 0, 0);

    // 渲染到反射目标
    const currentRenderTarget = renderer.getRenderTarget();
    renderer.setRenderTarget(this.reflectionRenderTarget);
    renderer.render(scene, this.reflectionCamera);
    renderer.setRenderTarget(currentRenderTarget);
  }

  /**
   * 渲染折射
   */
  private renderRefraction(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera): void {
    // 渲染到折射目标
    const currentRenderTarget = renderer.getRenderTarget();
    renderer.setRenderTarget(this.refractionRenderTarget);
    renderer.render(scene, camera);
    renderer.setRenderTarget(currentRenderTarget);
  }

  /**
   * 更新波浪纹理
   */
  private updateWaveTexture(): void {
    // 动态更新波浪纹理
    // 这里可以实现更复杂的波浪模拟
  }

  /**
   * 设置水体材质参数
   */
  public setWaterMaterialParams(params: Partial<WaterMaterialParams>): void {
    if (params.color) {
      this.waterMaterial.uniforms.waterColor.value = params.color;
    }
    if (params.opacity !== undefined) {
      this.waterMaterial.uniforms.opacity.value = params.opacity;
    }
    if (params.waveStrength !== undefined) {
      this.waterMaterial.uniforms.waveStrength.value = params.waveStrength;
    }
    if (params.waveSpeed !== undefined) {
      this.waterMaterial.uniforms.waveSpeed.value = params.waveSpeed;
    }
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): any {
    return {
      waterBodiesCount: this.waterBodies.size,
      reflectionResolution: this.config.reflectionResolution,
      refractionResolution: this.config.refractionResolution,
      quality: this.config.quality
    };
  }

  /**
   * 销毁资源
   */
  public dispose(): void {
    this.reflectionRenderTarget.dispose();
    this.refractionRenderTarget.dispose();
    this.waterMaterial.dispose();
    this.waveTexture.dispose();
    this.foamTexture.dispose();
    this.causticsTexture.dispose();
    
    Debug.log('增强水体渲染器资源已释放');
  }
}
